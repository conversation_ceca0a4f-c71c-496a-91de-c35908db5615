const mysql = require('mysql2/promise');

async function testCleanImplementation() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('🔗 Connected to MySQL database');
    console.log('🧪 TESTING CLEAN IMPLEMENTATION OF REJECTION WORKFLOW\n');

    // STEP 1: Create a test voucher for rejection workflow testing
    console.log('📄 STEP 1: CREATING TEST VOUCHER');
    console.log('=================================');

    const testVoucher = {
      voucher_id: 'FINJUL0001',
      claimant: '<PERSON>',
      description: 'Test voucher for rejection workflow',
      amount: 1000.00,
      status: 'VOUCHER CREATED',
      department: 'FINANCE',
      original_department: 'FINANCE',
      created_by: 'FELIX AYISI',
      created_at: new Date(),
      work_started: 0,
      received_by_audit: 0,
      dispatched: 0,
      deleted: 0
    };

    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, claimant, description, amount, status, department,
        original_department, created_by, created_at, work_started,
        received_by_audit, dispatched, deleted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      1, testVoucher.voucher_id, testVoucher.claimant, testVoucher.description,
      testVoucher.amount, testVoucher.status, testVoucher.department,
      testVoucher.original_department, testVoucher.created_by, testVoucher.created_at,
      testVoucher.work_started, testVoucher.received_by_audit, testVoucher.dispatched,
      testVoucher.deleted
    ]);

    console.log(`✅ Created test voucher: ${testVoucher.voucher_id}`);
    console.log(`   Claimant: ${testVoucher.claimant}`);
    console.log(`   Amount: ${testVoucher.amount}`);
    console.log(`   Status: ${testVoucher.status}`);
    console.log(`   Department: ${testVoucher.department}`);

    // STEP 2: Send voucher to Audit (simulate batch dispatch)
    console.log('\n📤 STEP 2: SENDING VOUCHER TO AUDIT');
    console.log('===================================');

    // Create a batch
    const batchId = 'BATCH-' + Date.now();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, from_department, to_department, created_by, created_at, received
      ) VALUES (?, ?, ?, ?, ?, ?)
    `, [batchId, 'FINANCE', 'AUDIT', 'FELIX AYISI', new Date(), 0]);

    // Add voucher to batch
    const [voucherResult] = await connection.execute('SELECT id FROM vouchers WHERE voucher_id = ?', [testVoucher.voucher_id]);
    const voucherId = voucherResult[0].id;

    await connection.execute(`
      INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)
    `, [batchId, voucherId]);

    // Update voucher status to dispatched
    await connection.execute(`
      UPDATE vouchers SET status = ?, dispatched = 1 WHERE id = ?
    `, ['VOUCHER DISPATCHED', voucherId]);

    console.log(`✅ Created batch: ${batchId}`);
    console.log(`✅ Added voucher to batch`);
    console.log(`✅ Updated voucher status to DISPATCHED`);

    // STEP 3: Verify system state before testing
    console.log('\n🔍 STEP 3: SYSTEM STATE VERIFICATION');
    console.log('====================================');

    const [currentVouchers] = await connection.execute(`
      SELECT id, voucher_id, status, department, original_department, 
             work_started, received_by_audit, dispatched
      FROM vouchers 
      WHERE voucher_id = ?
    `, [testVoucher.voucher_id]);

    if (currentVouchers.length > 0) {
      const v = currentVouchers[0];
      console.log(`📄 Voucher ${v.voucher_id}:`);
      console.log(`   ID: ${v.id}`);
      console.log(`   Status: ${v.status}`);
      console.log(`   Department: ${v.department} | Original: ${v.original_department}`);
      console.log(`   Work Started: ${v.work_started ? 'YES' : 'NO'}`);
      console.log(`   Received By Audit: ${v.received_by_audit ? 'YES' : 'NO'}`);
      console.log(`   Dispatched: ${v.dispatched ? 'YES' : 'NO'}`);
    }

    const [batchInfo] = await connection.execute(`
      SELECT * FROM voucher_batches WHERE id = ?
    `, [batchId]);

    if (batchInfo.length > 0) {
      const b = batchInfo[0];
      console.log(`📦 Batch ${b.id}:`);
      console.log(`   From: ${b.from_department} → To: ${b.to_department}`);
      console.log(`   Created By: ${b.created_by}`);
      console.log(`   Received: ${b.received ? 'YES' : 'NO'}`);
    }

    // STEP 4: Instructions for manual testing
    console.log('\n🎯 STEP 4: MANUAL TESTING INSTRUCTIONS');
    console.log('======================================');
    console.log('✅ Test voucher and batch have been created');
    console.log('✅ System is ready for rejection workflow testing');
    console.log('');
    console.log('📋 NEXT STEPS FOR MANUAL TESTING:');
    console.log('1. 🌐 Open VMS application in browser (http://localhost:8080)');
    console.log('2. 👤 Login as an Audit user (e.g., SAMUEL ASIEDU)');
    console.log('3. 📥 Go to Audit Dashboard → Incoming Batches');
    console.log(`4. 📦 Find batch: ${batchId}`);
    console.log('5. 🔍 Receive the batch (should use /receive-normal route)');
    console.log('6. ❌ Reject the voucher (should create rejection copy)');
    console.log('7. 📤 Dispatch rejected voucher back to Finance');
    console.log('8. 👤 Login as Finance user and verify rejected voucher appears');
    console.log('9. 🔄 Resubmit the rejected voucher');
    console.log('10. ✅ Verify resubmitted voucher follows correct workflow');
    console.log('');
    console.log('🔍 WHAT TO WATCH FOR:');
    console.log('- ✅ Server logs should show dedicated routes being used');
    console.log('- ❌ No "DEPRECATED ROUTE" messages should appear');
    console.log('- ✅ Rejected vouchers should appear in correct tabs');
    console.log('- ✅ Resubmitted vouchers should progress properly');
    console.log('');
    console.log('🎉 CLEAN IMPLEMENTATION IS READY FOR TESTING!');

  } catch (error) {
    console.error('❌ Error setting up test:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

testCleanImplementation();
