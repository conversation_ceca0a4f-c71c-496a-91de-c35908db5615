
const mysql = require('mysql2/promise');
const { logger } = require('../utils/logger.js');

let pool;

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_production',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

async function initializeDatabase() {
  try {
    // Create connection pool
    pool = mysql.createPool(dbConfig);
    
    // Test connection
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    
    logger.info('✅ Database connection pool initialized');
    
    // Ensure database exists
    await ensureDatabaseExists();
    await ensureTablesExist();
    
    return pool;
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    throw error;
  }
}

async function ensureDatabaseExists() {
  try {
    const tempConnection = await mysql.createConnection({
      host: dbConfig.host,
      user: dbConfig.user,
      password: dbConfig.password
    });
    
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database}`);
    await tempConnection.end();
    
    logger.info('✅ Database exists or created');
  } catch (error) {
    logger.error('❌ Failed to ensure database exists:', error);
    throw error;
  }
}

async function ensureTablesExist() {
  try {
    // Create essential tables if they don't exist
    const tables = [
      `CREATE TABLE IF NOT EXISTS vouchers (
        id VARCHAR(36) PRIMARY KEY,
        voucher_id VARCHAR(50) UNIQUE NOT NULL,
        claimant VARCHAR(255) NOT NULL,
        description TEXT,
        amount DECIMAL(15,2) NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'VOUCHER CREATED',
        department VARCHAR(50) NOT NULL,
        original_department VARCHAR(50),
        created_by VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        comment TEXT,
        is_rejection_copy BOOLEAN DEFAULT FALSE,
        rejection_type VARCHAR(50),
        parent_voucher_id VARCHAR(36),
        work_started BOOLEAN DEFAULT FALSE,
        received_by_audit BOOLEAN DEFAULT FALSE,
        dispatched BOOLEAN DEFAULT FALSE,
        deleted BOOLEAN DEFAULT FALSE,
        flags JSON,
        INDEX idx_status (status),
        INDEX idx_department (department),
        INDEX idx_voucher_id (voucher_id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS voucher_batches (
        id VARCHAR(36) PRIMARY KEY,
        department VARCHAR(50) NOT NULL,
        sent_by VARCHAR(255) NOT NULL,
        sent_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        received BOOLEAN DEFAULT FALSE,
        from_audit BOOLEAN DEFAULT FALSE,
        contains_rejected_vouchers BOOLEAN DEFAULT FALSE,
        rejected_voucher_count INT DEFAULT 0,
        INDEX idx_department (department),
        INDEX idx_received (received)
      )`,
      
      `CREATE TABLE IF NOT EXISTS batch_vouchers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        batch_id VARCHAR(36) NOT NULL,
        voucher_id VARCHAR(36) NOT NULL,
        FOREIGN KEY (batch_id) REFERENCES voucher_batches(id),
        FOREIGN KEY (voucher_id) REFERENCES vouchers(id),
        INDEX idx_batch_id (batch_id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        department VARCHAR(50) NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        active BOOLEAN DEFAULT TRUE
      )`
    ];
    
    for (const tableSQL of tables) {
      await query(tableSQL);
    }
    
    logger.info('✅ All essential tables verified/created');
  } catch (error) {
    logger.error('❌ Failed to create tables:', error);
    throw error;
  }
}

async function query(sql, params = []) {
  if (!pool) {
    await initializeDatabase();
  }
  
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    logger.error('Database query error:', error);
    throw error;
  }
}

async function getTransaction() {
  if (!pool) {
    await initializeDatabase();
  }
  
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  
  return {
    query: async (sql, params) => {
      const [rows] = await connection.execute(sql, params);
      return [rows];
    },
    commit: () => connection.commit(),
    rollback: () => connection.rollback(),
    release: () => connection.release()
  };
}

module.exports = { 
  query, 
  getTransaction, 
  initializeDatabase 
};
