
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { formatCurrentDate } from '../utils';

export interface VoucherBatchesSlice {
  voucherBatches: AppState['voucherBatches'];
  fetchBatches: () => Promise<void>;
  createVoucherBatch: AppState['createVoucherBatch'];
  receiveVoucherBatch: AppState['receiveVoucherBatch'];
}

export const createVoucherBatchesSlice: StateCreator<AppState, [], [], VoucherBatchesSlice> = (set, get) => ({
  voucherBatches: [],

  // Fetch batches from API and sync with local state
  fetchBatches: async () => {
    try {
      console.log('🔄 Starting fetchBatches...');

      // FIXED: Check for current user instead of token
      const currentUser = get().currentUser;
      if (!currentUser) {
        console.log('❌ No current user found for fetchBatches');
        return;
      }

      console.log('🔄 Making API call to /api/batches...');
      const response = await fetch('/api/batches', {
        credentials: 'include' // FIXED: Use session cookies
      });

      console.log('🔄 API response status:', response.status);

      if (response.ok) {
        const batches = await response.json();
        console.log('✅ Fetched batches from API:', batches);
        console.log('✅ Number of batches fetched:', batches.length);

        // Convert API batches to local format
        const localBatches = batches.map((batch: any) => ({
          id: batch.id,
          department: batch.department,
          voucherIds: batch.voucherIds || [],
          vouchers: batch.vouchers || [],  // CRITICAL FIX: Include vouchers array
          sentBy: batch.sent_by,
          sentTime: batch.sent_time,
          received: batch.received,
          fromAudit: batch.from_audit
        }));

        console.log('✅ Converted to local format:', localBatches);
        set({ voucherBatches: localBatches });
        console.log('✅ Updated store with batches');
      } else {
        console.error('❌ API response not ok:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('❌ Error response body:', errorText);
      }
    } catch (error) {
      console.error('❌ Error fetching batches:', error);
    }
  },
  createVoucherBatch: () => {
    // PRODUCTION: Local batch creation is disabled to prevent data inconsistency
    // All batch creation must go through the API to ensure database persistence
    throw new Error('Local batch creation is disabled. Use API-based batch creation only.');
  },
  receiveVoucherBatch: async (batchId, receivedVoucherIds, rejectedVoucherIds, rejectionComments = {}) => {
    console.log(`Receiving batch ${batchId} - Accepted: ${receivedVoucherIds.length}, Rejected: ${rejectedVoucherIds.length}`);

    // FIXED: Check for current user instead of token
    const currentUser = get().currentUser;
    if (!currentUser) {
      throw new Error('Authentication required. Please log in again.');
    }

    try {
      // 🔒 PRODUCTION-GRADE SMART ROUTING: Automatically detect voucher types and use correct endpoints
      console.log('🔍 SMART ROUTING: Analyzing batch composition...');

      // STEP 1: Get batch details to understand voucher types
      const batchResponse = await fetch(`/api/batches/${batchId}`, {
        credentials: 'include'
      });

      if (!batchResponse.ok) {
        throw new Error(`Failed to fetch batch details: ${batchResponse.statusText}`);
      }

      const batch = await batchResponse.json();
      const vouchers = batch.vouchers || [];

      // STEP 2: Categorize vouchers by type
      const normalVouchers: string[] = [];
      const rejectedVouchers: string[] = [];
      const resubmittedVouchers: string[] = [];

      receivedVoucherIds.forEach(voucherId => {
        const voucher = vouchers.find((v: any) => v.id === voucherId);
        if (!voucher) {
          console.warn(`⚠️ SMART ROUTING: Voucher ${voucherId} not found in batch`);
          return;
        }

        // Categorize based on voucher properties
        if (voucher.status === 'VOUCHER REJECTED' || voucher.is_rejection_copy || voucher.rejected_by) {
          rejectedVouchers.push(voucherId);
          console.log(`📋 SMART ROUTING: Categorized ${voucherId} as REJECTED voucher`);
        } else if (voucher.resubmission_count > 0 || (voucher.comment && voucher.comment.includes('Re-added from rejection'))) {
          resubmittedVouchers.push(voucherId);
          console.log(`📋 SMART ROUTING: Categorized ${voucherId} as RESUBMITTED voucher`);
        } else {
          normalVouchers.push(voucherId);
          console.log(`📋 SMART ROUTING: Categorized ${voucherId} as NORMAL voucher`);
        }
      });

      console.log(`📊 SMART ROUTING: Batch composition - Normal: ${normalVouchers.length}, Rejected: ${rejectedVouchers.length}, Resubmitted: ${resubmittedVouchers.length}`);

      // STEP 3: Call appropriate endpoints for each voucher type
      const results: any[] = [];

      // Process normal vouchers
      if (normalVouchers.length > 0) {
        console.log(`🔄 SMART ROUTING: Processing ${normalVouchers.length} normal vouchers...`);
        const normalResponse = await fetch(`/api/batches/${batchId}/receive-normal`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ receivedVoucherIds: normalVouchers })
        });

        if (!normalResponse.ok) {
          const errorText = await normalResponse.text();
          throw new Error(`Normal voucher processing failed: ${normalResponse.status} - ${errorText}`);
        }

        const normalResult = await normalResponse.json();
        results.push({ type: 'normal', count: normalVouchers.length, result: normalResult });
        console.log(`✅ SMART ROUTING: Normal vouchers processed successfully`);
      }

      // Process rejected vouchers
      if (rejectedVouchers.length > 0) {
        console.log(`🔄 SMART ROUTING: Processing ${rejectedVouchers.length} rejected vouchers...`);
        const rejectedResponse = await fetch(`/api/batches/${batchId}/receive-rejected`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ receivedVoucherIds: rejectedVouchers })
        });

        if (!rejectedResponse.ok) {
          const errorText = await rejectedResponse.text();
          throw new Error(`Rejected voucher processing failed: ${rejectedResponse.status} - ${errorText}`);
        }

        const rejectedResult = await rejectedResponse.json();
        results.push({ type: 'rejected', count: rejectedVouchers.length, result: rejectedResult });
        console.log(`✅ SMART ROUTING: Rejected vouchers processed successfully`);
      }

      // Process resubmitted vouchers
      if (resubmittedVouchers.length > 0) {
        console.log(`🔄 SMART ROUTING: Processing ${resubmittedVouchers.length} resubmitted vouchers...`);
        const resubmittedResponse = await fetch(`/api/batches/${batchId}/receive-resubmitted`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ receivedVoucherIds: resubmittedVouchers })
        });

        if (!resubmittedResponse.ok) {
          const errorText = await resubmittedResponse.text();
          throw new Error(`Resubmitted voucher processing failed: ${resubmittedResponse.status} - ${errorText}`);
        }

        const resubmittedResult = await resubmittedResponse.json();
        results.push({ type: 'resubmitted', count: resubmittedVouchers.length, result: resubmittedResult });
        console.log(`✅ SMART ROUTING: Resubmitted vouchers processed successfully`);
      }

      console.log('✅ SMART ROUTING: All voucher types processed successfully:', results);
      const result = { success: true, results, message: 'All voucher types processed successfully' };

      // Update local state to match database
      const batch = get().voucherBatches.find(b => b.id === batchId);
      if (batch) {
        set((state) => ({
          voucherBatches: state.voucherBatches.map(b =>
            b.id === batchId ? { ...b, received: true } : b
          )
        }));
      }

      const currentUser = get().currentUser;
      const currentTime = formatCurrentDate();

      // ARCHITECTURAL FIX: Skip frontend voucher updates - backend handles all updates
      // The backend batch receiving endpoint already updates voucher statuses, departments, and all fields
      // Frontend updates cause 403 errors because vouchers are in different departments during transition
      console.log('🔄 BATCH RECEIVING: Backend handles all voucher updates - skipping frontend updates to prevent 403 errors');

      // Log what would have been updated for debugging
      receivedVoucherIds.forEach(id => {
        const voucher = get().vouchers.find(v => v.id === id);
        if (voucher) {
          if (batch?.fromAudit) {
            console.log(`📄 Voucher ${voucher.voucherId}: Backend will update status and transfer back to ${voucher.originalDepartment || 'original department'}`);
          } else {
            console.log(`📄 Voucher ${voucher.voucherId}: Backend will transfer to AUDIT and set status to AUDIT: PROCESSING`);
          }
        }
      });

      // ARCHITECTURAL FIX: Skip frontend updates for rejected vouchers too - backend handles all updates
      rejectedVoucherIds.forEach(id => {
        const voucher = get().vouchers.find(v => v.id === id);
        if (voucher) {
          const rejectionComment = rejectionComments[id] || "Rejected during batch receiving";
          console.log(`📄 Voucher ${voucher.voucherId}: Backend will reject with comment: ${rejectionComment}`);

          // Backend handles all voucher status updates - no local tracking needed

          // Notify department (this doesn't require voucher updates)
          const departmentUser = get().users.find(u => u.department === voucher.originalDepartment || voucher.department);
          if (departmentUser) {
            get().addNotification({
              userId: departmentUser.id,
              message: `VOUCHER ${voucher.voucherId} REJECTED BY AUDIT: ${rejectionComment}`,
              voucherId: id,
              type: "VOUCHER_REJECTED"
            });
          }
        }
      });

      // REMOVED: batchProcessed and batchProcessedTime fields don't exist in database schema
      // The batch receiving process already updates voucher status properly on the server side
      console.log('✅ AUDIT: Batch receiving completed successfully - voucher status updated by server');

      // CRITICAL FIX: Refresh vouchers after batch processing to update department hubs
      console.log('🔄 AUDIT: Refreshing vouchers after batch processing to update department hubs...');

      // Add delay to ensure database transaction has committed
      console.log('⏳ AUDIT: Waiting 2 seconds for database transaction to commit...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // CRITICAL FIX: Always fetch ALL vouchers so departments can see vouchers sent to audit
      console.log('🔄 🚀🚀🚀 CACHE BUSTER: AUDIT batch processing - fetching ALL vouchers');
      await get().fetchVouchers(); // Always fetch all vouchers
      console.log('✅ AUDIT: Vouchers refreshed after batch processing');

      console.log(`✅ Successfully received batch ${batchId}`);

    } catch (error) {
      console.error('❌ Failed to receive batch:', error);
      throw error;
    }
  },
});
